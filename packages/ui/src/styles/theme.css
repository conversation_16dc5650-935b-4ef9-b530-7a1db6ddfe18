/*
 * SPDX-License-Identifier: AGPL-3.0-only
 * theme.css
 * Copyright (C) 2025 Nextify Limited
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as
 * published by the Free Software Foundation, either version 3 of the
 * License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 *
 */

@custom-variant dark (&:where(.dark, .dark *));

@theme inline {
  --color-brand: var(--brand);
  --color-brand-foreground: var(--brand-foreground);
  --color-light: var(--light);
  --color-light-foreground: var(--light-foreground);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --radius-2xl: calc(var(--radius) + 8px);

  --spacing-container: 1280px;
  --spacing-container-lg: 1536px;

  --shadow-md: 0 4px 6px -1px var(--shadow), 0 2px 4px -2px var(--shadow);
  --shadow-xl: 0 20px 25px -5px var(--shadow), 0 8px 10px -6px var(--shadow);
  --shadow-2xl: 0 25px 50px -12px var(--shadow);
  --shadow-mockup: -12px 16px 48px var(--shadow-strong);

  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
  --animate-appear: appear 0.4s forwards ease-out;
  --animate-appear-zoom: appear-zoom 0.4s forwards ease-out;

  --animate-rainbow: rainbow var(--speed, 2s) infinite linear;

  @keyframes rainbow {
    0% {
      background-position: 0%;
    }
    100% {
      background-position: 200%;
    }
  }

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }
  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
  @keyframes appear {
    0% {
      opacity: 0;
      transform: translateY(1rem);
      will-change: opacity, transform;
    }
    100% {
      opacity: 1;
      transform: translateY(0);
      will-change: auto;
    }
  }
  @keyframes appear-zoom {
    0% {
      opacity: 0;
      transform: scale(0.5);
      will-change: opacity, transform;
    }
    100% {
      opacity: 1;
      transform: scale(1);
      will-change: auto;
    }
  }
  --animate-marquee: marquee var(--duration) infinite linear;
  --animate-appear-zoom-fast: appear-zoom 0.25s forwards ease-out;
  --animate-hover: hover 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  --animate-hover-reverse: hover-reverse 6s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  --animate-pulse-fade: pulse-fade 6s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  --animate-pulse-hover: pulse-hover 6s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  --animate-spin-slow: spin 3s linear infinite;
  --animate-wiggle: wiggle 1s ease-in-out infinite;
  --animate-impulse: impulse 2s ease-in-out infinite;
  --animate-appear-slide: appear-slide 0.4s forwards cubic-bezier(0.4, 0.18, 0.52, 1.6);
  --animate-orbit: orbit 2s linear infinite;
  --animate-rotate: rotate 8s linear infinite;
  --animate-shiny-text: shiny-text 2.5s infinite;
  --animate-reveal: reveal 3s forwards;

  --transition-delay-1500: 1000ms;
  --transition-delay-2000: 1200ms;

  --inset-shadow-md: inset 0 2px 12px rgb(0 0 0 / 0.05);
  --inset-shadow-lg: inset 0 2px 24px rgb(0 0 0 / 0.05);
  --inset-shadow-xl: inset 0 2px 32px rgb(0 0 0 / 0.05);
  @keyframes marquee {
    from {
      transform: translateX(0);
    }
    to {
      transform: translateX(calc(-100% - var(--gap)));
    }
  }
  @keyframes pulse-hover {
    0% {
      opacity: 1;
      transform: translateY(0);
      will-change: opacity, transform;
    }
    50% {
      opacity: 0.5;
      transform: translateY(-1rem);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
      will-change: auto;
    }
  }
  @keyframes hover {
    0% {
      transform: translateY(0) translateX(0);
      will-change: transform;
    }
    50% {
      transform: translateY(-1rem) translateX(1rem);
    }
    100% {
      transform: translateY(0) translateX(0);
      will-change: auto;
    }
  }
  @keyframes hover-reverse {
    0% {
      transform: translateY(0) translateX(0);
      will-change: transform;
    }
    50% {
      transform: translateY(1rem) translateX(1rem);
    }
    100% {
      transform: translateY(0) translateX(0);
      will-change: auto;
    }
  }
  @keyframes pulse-fade {
    0% {
      opacity: 1;
      will-change: opacity;
    }
    50% {
      opacity: 0.3;
    }
    100% {
      opacity: 1;
      will-change: auto;
    }
  }
  @keyframes wiggle {
    0%,
    16.67%,
    33.33%,
    50% {
      transform: rotate(-15deg);
      will-change: transform;
    }
    8.33%,
    25%,
    41.67% {
      transform: rotate(15deg);
    }
    50%,
    100% {
      transform: rotate(0deg);
      will-change: auto;
    }
  }
  @keyframes impulse {
    20% {
      left: 0;
      transform: scale(0.5);
      opacity: 0;
      will-change: transform, opacity, left;
    }
    50% {
      opacity: 1;
      left: 50%;
      transform: scale(3);
    }
    80% {
      opacity: 0;
      left: 100%;
      transform: scale(0.5);
      will-change: auto;
    }
  }
  @keyframes orbit {
    0% {
      stroke-dashoffset: 500;
      opacity: 0;
      will-change: stroke-dashoffset, opacity;
    }
    10%,
    20% {
      opacity: 1;
    }
    35% {
      opacity: 0;
    }
    40% {
      opacity: 0;
      stroke-dashoffset: -250;
      will-change: auto;
    }
  }
  @keyframes appear-slide {
    0% {
      opacity: 0;
      transform: translateY(3rem) scale(0.5);
      will-change: opacity, transform;
    }
    100% {
      opacity: 1;
      transform: translateY(0) scale(1);
      will-change: auto;
    }
  }
  @keyframes rotate {
    0% {
      transform: rotate(0deg);
      will-change: transform;
    }
    100% {
      transform: rotate(360deg);
      will-change: auto;
    }
  }
  @keyframes shiny-text {
    0% {
      background-position: calc(-100% - 200px) 0;
      will-change: background-position;
    }
    100% {
      background-position: calc(100% + 200px) 0;
      will-change: auto;
    }
  }
  @keyframes reveal {
    0% {
      filter: blur(10px);
      opacity: 0;
    }
    100% {
      filter: blur(0px);
      opacity: 1;
    }
  }
}
