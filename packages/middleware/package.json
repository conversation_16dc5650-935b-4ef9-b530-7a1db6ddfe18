{"name": "@libra/middleware", "version": "1.0.0", "description": "Shared middleware for Libra applications", "type": "module", "main": "./src/index.ts", "module": "./src/index.ts", "types": "./src/index.ts", "exports": {".": {"import": "./src/index.ts", "types": "./src/index.ts"}, "./cors": {"import": "./src/cors.ts", "types": "./src/cors.ts"}, "./error-handler": {"import": "./src/error-handler.ts", "types": "./src/error-handler.ts"}, "./logging": {"import": "./src/logging.ts", "types": "./src/logging.ts"}, "./rate-limit": {"import": "./src/rate-limit.ts", "types": "./src/rate-limit.ts"}, "./auth": {"import": "./src/auth.ts", "types": "./src/auth.ts"}, "./types-universal": {"import": "./src/types-universal.ts", "types": "./src/types-universal.ts"}, "./universal": {"import": "./src/index-universal.ts", "types": "./src/index-universal.ts"}, "./error-handler-universal": {"import": "./src/error-handler-universal.ts", "types": "./src/error-handler-universal.ts"}}, "files": ["src/**/*", "README.md"], "scripts": {"typecheck": "tsc --noEmit", "test": "bun test"}, "dependencies": {"hono": "^4.8.4", "zod": "^4.0.14", "@libra/common": "*"}, "devDependencies": {"@libra/typescript-config": "*", "@cloudflare/workers-types": "^4.20250712.0", "typescript": "^5.8.3"}, "peerDependencies": {"hono": "^4.8.4"}, "keywords": ["middleware", "hono", "cloudflare-workers", "cors", "error-handling", "logging", "rate-limiting"], "license": "AGPL-3.0"}