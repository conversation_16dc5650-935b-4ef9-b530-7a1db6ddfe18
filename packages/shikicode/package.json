{"name": "@libra/shikicode", "version": "1.0.0", "private": true, "description": "shikicode", "type": "module", "main": "lib/index.js", "types": "./src/index.ts", "scripts": {"build": "tsup", "clean": "rm -rf dist .turbo node_modules", "typecheck": "tsc --noEmit", "update": "bun update"}, "devDependencies": {"shiki": "^3.9.1", "@libra/typescript-config": "*"}, "exports": {".": {"types": "./src/index.ts", "import": {"development": "./src/index.ts", "default": "./lib/index.js"}, "default": "./lib/index.js"}, "./plugins": {"types": "./src/plugins/index.ts", "import": {"development": "./src/plugins/index.ts", "default": "./lib/plugins/index.js"}, "default": "./lib/plugins/index.js"}, "./package.json": "./package.json"}}